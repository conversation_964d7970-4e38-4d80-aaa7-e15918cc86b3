#!/usr/bin/env python3
"""
源域协方差预计算脚本

用于从源域数据计算layer3特征的协方差矩阵，并保存为.pt文件供Co-DGP使用。
"""

import os
import torch
import argparse
from torch.utils.data import DataLoader
from networks.ResUnet_TTA import ResUnet
from utils.convert import AdaBN
from utils.covariance_utils import compute_source_covariance_from_model, save_source_covariance
from dataloaders.OPTIC_dataloader import OPTIC_dataset
from dataloaders.transform import collate_fn_wo_transform
from dataloaders.convert_csv_to_list import convert_labeled_list


def main():
    parser = argparse.ArgumentParser(description='Precompute source domain covariance matrix')
    
    # 基本参数
    parser.add_argument('--source_dataset', type=str, default='RIM_ONE_r3',
                        help='Source dataset name (RIM_ONE_r3/REFUGE/ORIGA/REFUGE_Valid/Drishti_GS)')
    parser.add_argument('--model_path', type=str, default='./models',
                        help='Path to the trained model directory')
    parser.add_argument('--data_root', type=str, default='/media/userdisk0/zychen/Datasets/Fundus',
                        help='Root directory of the dataset')
    parser.add_argument('--backbone', type=str, default='resnet34',
                        choices=['resnet34', 'resnet50'],
                        help='Backbone architecture')
    parser.add_argument('--num_classes', type=int, default=2,
                        help='Number of output classes')
    parser.add_argument('--batch_size', type=int, default=8,
                        help='Batch size for processing')
    parser.add_argument('--max_samples', type=int, default=100,
                        help='Maximum number of samples to process')
    parser.add_argument('--device', type=str, default='cuda:0',
                        help='Device to use (cuda:0/cpu)')
    parser.add_argument('--target_layer', type=str, default='layer3',
                        help='Target layer for covariance computation')
    
    args = parser.parse_args()
    
    # 设置设备
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # 加载源域数据列表
    source_csv_list = [args.source_dataset + '_train.csv']
    source_img_list, source_label_list = convert_labeled_list(args.data_root, source_csv_list)
    print(f"Found {len(source_img_list)} source domain samples")
    
    # 创建数据加载器
    source_dataset = OPTIC_dataset(
        args.data_root, source_img_list, source_label_list,
        512, img_normalize=True
    )
    
    source_dataloader = DataLoader(
        source_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=4,
        collate_fn=collate_fn_wo_transform
    )
    
    # 加载预训练模型
    model_load_path = os.path.join(args.model_path, args.source_dataset)
    print(f"Loading model from: {model_load_path}")
    model = ResUnet(
        resnet=args.backbone,
        num_classes=args.num_classes,
        pretrained=False,
        newBN=AdaBN,
        warm_n=5,
        gat_hidden_dim=128,
        gat_num_heads=4,
        gat_num_layers=2,
        gat_dropout=0.1
    ).to(device)

    checkpoint_path = os.path.join(model_load_path, 'last-Res_Unet.pth')
    if not os.path.exists(checkpoint_path):
        raise FileNotFoundError(f"Model checkpoint not found: {checkpoint_path}")
    
    checkpoint = torch.load(checkpoint_path, map_location=device)
    model.load_state_dict(checkpoint, strict=True)
    print("Model loaded successfully")
    
    # 计算协方差矩阵
    print(f"Computing source covariance matrix for {args.target_layer}...")
    source_cov = compute_source_covariance_from_model(
        model=model,
        source_dataloader=source_dataloader,
        target_layer_name=args.target_layer,
        device=device
    )
    
    print(f"Source covariance matrix shape: {source_cov.shape}")
    print(f"Covariance matrix statistics:")
    print(f"  Mean: {source_cov.mean().item():.6f}")
    print(f"  Std: {source_cov.std().item():.6f}")
    print(f"  Min: {source_cov.min().item():.6f}")
    print(f"  Max: {source_cov.max().item():.6f}")
    
    # 验证协方差矩阵
    from utils.covariance_utils import validate_covariance_matrix
    expected_channels = 256 if args.backbone == 'resnet34' else 512
    is_valid = validate_covariance_matrix(source_cov, expected_channels)
    
    if not is_valid:
        print("WARNING: Computed covariance matrix failed validation!")
    else:
        print("Covariance matrix validation passed")
    
    # 保存协方差矩阵
    covariance_save_path = os.path.join(model_load_path, 'source_covariance.pt')
    save_source_covariance(source_cov, covariance_save_path)
    
    print(f"Source covariance matrix saved to: {covariance_save_path}")
    print("Precomputation completed successfully!")


if __name__ == '__main__':
    main()
