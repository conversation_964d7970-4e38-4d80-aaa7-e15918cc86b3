import torch
import torch.nn as nn
import torch.nn.functional as F
import math


class GraphAttentionLayer(nn.Module):
    """
    单层图注意力网络层
    
    实现GAT的核心注意力机制，用于学习通道间的关系
    """
    
    def __init__(self, in_features, out_features, dropout=0.1, alpha=0.2):
        """
        初始化图注意力层
        
        Args:
            in_features (int): 输入特征维度
            out_features (int): 输出特征维度  
            dropout (float): dropout概率
            alpha (float): LeakyReLU负斜率
        """
        super(GraphAttentionLayer, self).__init__()
        self.in_features = in_features
        self.out_features = out_features
        self.dropout = dropout
        self.alpha = alpha
        
        # 线性变换矩阵
        self.W = nn.Parameter(torch.empty(size=(in_features, out_features)))
        nn.init.xavier_uniform_(self.W.data, gain=1.414)
        
        # 注意力机制参数
        self.a = nn.Parameter(torch.empty(size=(2 * out_features, 1)))
        nn.init.xavier_uniform_(self.a.data, gain=1.414)
        
        self.leakyrelu = nn.LeakyReLU(self.alpha)
        
    def forward(self, h, adj=None, return_attention=False):
        """
        前向传播

        Args:
            h (torch.Tensor): 输入节点特征 (N, in_features)
            adj (torch.Tensor): 邻接矩阵，None表示全连接图
            return_attention (bool): 是否返回注意力权重

        Returns:
            torch.Tensor or tuple: 输出节点特征 (N, out_features)，
                                 如果return_attention=True则返回(output, attention)
        """
        # 线性变换
        Wh = torch.mm(h, self.W)  # (N, out_features)
        N = Wh.size()[0]

        # 计算注意力系数
        # 为每对节点创建特征拼接
        Wh1 = torch.mm(Wh, self.a[:self.out_features, :])  # (N, 1)
        Wh2 = torch.mm(Wh, self.a[self.out_features:, :])  # (N, 1)

        # 广播计算所有节点对的注意力分数
        e = self.leakyrelu(Wh1 + Wh2.T)  # (N, N)

        # 对于全连接图，不需要mask
        if adj is None:
            attention = F.softmax(e, dim=1)
        else:
            # 如果提供邻接矩阵，则mask非邻接节点
            e = e.masked_fill(adj == 0, -9e15)
            attention = F.softmax(e, dim=1)

        # 应用dropout
        attention = F.dropout(attention, self.dropout, training=self.training)

        # 聚合邻居特征
        h_prime = torch.mm(attention, Wh)  # (N, out_features)

        if return_attention:
            return h_prime, attention
        else:
            return h_prime


class MultiHeadGraphAttention(nn.Module):
    """
    多头图注意力网络
    
    将多个注意力头的输出进行拼接或平均
    """
    
    def __init__(self, in_features, out_features, num_heads, dropout=0.1, alpha=0.2, concat=True):
        """
        初始化多头注意力
        
        Args:
            in_features (int): 输入特征维度
            out_features (int): 每个头的输出特征维度
            num_heads (int): 注意力头数
            dropout (float): dropout概率
            alpha (float): LeakyReLU负斜率
            concat (bool): 是否拼接多头输出，False则平均
        """
        super(MultiHeadGraphAttention, self).__init__()
        self.num_heads = num_heads
        self.concat = concat
        
        # 创建多个注意力头
        self.attentions = nn.ModuleList([
            GraphAttentionLayer(in_features, out_features, dropout=dropout, alpha=alpha)
            for _ in range(num_heads)
        ])
    
    def forward(self, h, adj=None, return_attention=False):
        """
        前向传播

        Args:
            h (torch.Tensor): 输入节点特征 (N, in_features)
            adj (torch.Tensor): 邻接矩阵
            return_attention (bool): 是否返回注意力权重

        Returns:
            torch.Tensor or tuple: 输出节点特征，
                                 如果return_attention=True则返回(output, attention)
        """
        if return_attention:
            head_outputs = []
            head_attentions = []
            for att in self.attentions:
                output, attention = att(h, adj, return_attention=True)
                head_outputs.append(output)
                head_attentions.append(attention)

            # 聚合输出
            if self.concat:
                final_output = torch.cat(head_outputs, dim=1)  # (N, num_heads * out_features)
            else:
                final_output = torch.mean(torch.stack(head_outputs), dim=0)  # (N, out_features)

            # 平均多头注意力权重
            aggregated_attention = torch.mean(torch.stack(head_attentions), dim=0)  # (N, N)
            return final_output, aggregated_attention
        else:
            # 计算所有头的输出
            head_outputs = [att(h, adj) for att in self.attentions]

            if self.concat:
                # 拼接所有头的输出
                return torch.cat(head_outputs, dim=1)  # (N, num_heads * out_features)
            else:
                # 平均所有头的输出
                return torch.mean(torch.stack(head_outputs), dim=0)  # (N, out_features)


class GAT(nn.Module):
    """
    完整的图注意力网络
    
    支持多层GAT堆叠，用于Co-DGP中的通道关系建模
    """
    
    def __init__(self, input_dim, hidden_dim, output_dim, num_heads, num_layers, dropout=0.1):
        """
        初始化GAT网络
        
        Args:
            input_dim (int): 输入特征维度
            hidden_dim (int): 隐藏层特征维度
            output_dim (int): 输出特征维度
            num_heads (int): 注意力头数
            num_layers (int): GAT层数
            dropout (float): dropout概率
        """
        super(GAT, self).__init__()
        self.num_layers = num_layers
        self.dropout = dropout
        
        # 构建GAT层
        self.gat_layers = nn.ModuleList()
        
        # 第一层：input_dim -> hidden_dim
        self.gat_layers.append(
            MultiHeadGraphAttention(
                input_dim, hidden_dim, num_heads, 
                dropout=dropout, concat=True
            )
        )
        
        # 中间层：(hidden_dim * num_heads) -> hidden_dim
        for _ in range(num_layers - 2):
            self.gat_layers.append(
                MultiHeadGraphAttention(
                    hidden_dim * num_heads, hidden_dim, num_heads,
                    dropout=dropout, concat=True
                )
            )
        
        # 最后一层：(hidden_dim * num_heads) -> output_dim
        if num_layers > 1:
            self.gat_layers.append(
                MultiHeadGraphAttention(
                    hidden_dim * num_heads, output_dim, 1,
                    dropout=dropout, concat=False
                )
            )
        else:
            # 如果只有一层，直接输出到output_dim
            self.gat_layers[0] = MultiHeadGraphAttention(
                input_dim, output_dim, num_heads,
                dropout=dropout, concat=False
            )
    
    def forward(self, h, adj=None, return_attention=False):
        """
        前向传播

        Args:
            h (torch.Tensor): 输入节点特征 (N, input_dim)
            adj (torch.Tensor): 邻接矩阵，None表示全连接图
            return_attention (bool): 是否返回注意力权重

        Returns:
            torch.Tensor or tuple: 输出节点特征 (N, output_dim)，
                                 如果return_attention=True则返回(output, layer_attentions)
        """
        layer_attentions = []

        # 逐层传播
        for i, gat_layer in enumerate(self.gat_layers):
            if return_attention:
                h, attention = gat_layer(h, adj, return_attention=True)
                layer_attentions.append(attention)
            else:
                h = gat_layer(h, adj, return_attention=False)

            # 除了最后一层，都应用ELU激活和dropout
            if i < self.num_layers - 1:
                h = F.elu(h)
                h = F.dropout(h, self.dropout, training=self.training)

        if return_attention:
            return h, layer_attentions
        else:
            return h
