import torch
import torch.nn as nn
import torch.nn.functional as F
from .graph_attention import GAT


class CoDGPModule(nn.Module):
    """
    Co-DGP (Covariance-aware Dynamic Graph Prompting) 模块
    
    核心功能：
    1. 将特征通道建模为图节点
    2. 使用GAT学习通道间关系
    3. 生成通道调制参数进行自适应特征变换
    """
    
    def __init__(self, num_channels, gat_hidden_dim=128, gat_num_heads=4, 
                 gat_num_layers=2, gat_dropout=0.1):
        """
        初始化Co-DGP模块
        
        Args:
            num_channels (int): 输入特征通道数
            gat_hidden_dim (int): GAT隐藏层维度
            gat_num_heads (int): GAT注意力头数
            gat_num_layers (int): GAT层数
            gat_dropout (float): GAT dropout率
        """
        super(CoDGPModule, self).__init__()
        self.num_channels = num_channels
        self.gat_hidden_dim = gat_hidden_dim
        
        # 通道特征投影层：将全局平均池化后的通道特征投影到GAT输入维度
        self.channel_projection = nn.Sequential(
            nn.Linear(1, gat_hidden_dim // 2),
            nn.ReLU(inplace=True),
            nn.Linear(gat_hidden_dim // 2, gat_hidden_dim),
            nn.ReLU(inplace=True)
        )
        
        # 图注意力网络：学习通道间关系
        self.gat = GAT(
            input_dim=gat_hidden_dim,
            hidden_dim=gat_hidden_dim,
            output_dim=gat_hidden_dim,
            num_heads=gat_num_heads,
            num_layers=gat_num_layers,
            dropout=gat_dropout
        )
        
        # 通道调制参数预测头
        self.modulation_head = nn.Sequential(
            nn.Linear(gat_hidden_dim, gat_hidden_dim // 2),
            nn.ReLU(inplace=True),
            nn.Linear(gat_hidden_dim // 2, 2)  # 输出gamma和beta
        )
        
        # 初始化权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化模块权重"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
        
        # 调制参数的特殊初始化：gamma初始化为1，beta初始化为0
        with torch.no_grad():
            # 最后一层的权重和偏置特殊处理
            final_layer = self.modulation_head[-1]
            # 输出维度为2：[gamma, beta]
            final_layer.bias[0] = 1.0  # gamma初始化为1
            final_layer.bias[1] = 0.0  # beta初始化为0

            # 权重初始化为小值，确保初始时调制接近恒等变换
            nn.init.normal_(final_layer.weight, mean=0.0, std=0.01)
    
    def forward(self, features):
        """
        前向传播
        
        Args:
            features (torch.Tensor): 输入特征 (B, C, H, W)
            
        Returns:
            torch.Tensor: 调制后的特征 (B, C, H, W)
        """
        B, C, H, W = features.shape
        
        # 1. 构建图节点表示：每个通道作为一个节点
        # 对每个通道进行全局平均池化
        channel_features = F.adaptive_avg_pool2d(features, (1, 1))  # (B, C, 1, 1)
        channel_features = channel_features.view(B, C, 1)  # (B, C, 1)
        
        # 投影到GAT输入空间
        node_features = self.channel_projection(channel_features)  # (B, C, gat_hidden_dim)
        
        # 2. 对每个样本独立应用GAT
        modulated_features_list = []
        
        for b in range(B):
            # 提取单个样本的节点特征
            sample_nodes = node_features[b]  # (C, gat_hidden_dim)
            
            # 应用GAT学习通道间关系
            enhanced_nodes = self.gat(sample_nodes)  # (C, gat_hidden_dim)
            
            # 生成调制参数
            modulation_params = self.modulation_head(enhanced_nodes)  # (C, 2)
            gamma = modulation_params[:, 0].unsqueeze(0).unsqueeze(-1).unsqueeze(-1)  # (1, C, 1, 1)
            beta = modulation_params[:, 1].unsqueeze(0).unsqueeze(-1).unsqueeze(-1)   # (1, C, 1, 1)
            
            # 应用通道调制
            sample_features = features[b:b+1]  # (1, C, H, W)
            modulated_sample = gamma * sample_features + beta  # (1, C, H, W)
            modulated_features_list.append(modulated_sample)
        
        # 合并所有样本
        modulated_features = torch.cat(modulated_features_list, dim=0)  # (B, C, H, W)
        
        return modulated_features
    
    def get_attention_weights(self, features):
        """
        获取注意力权重用于可视化分析

        Args:
            features (torch.Tensor): 输入特征 (B, C, H, W)

        Returns:
            list: 每个样本的注意力权重矩阵列表，格式为List[List[torch.Tensor]]
        """
        B, C, H, W = features.shape

        # 构建节点表示
        channel_features = F.adaptive_avg_pool2d(features, (1, 1))
        channel_features = channel_features.view(B, C, 1)
        node_features = self.channel_projection(channel_features)

        attention_weights_list = []

        for b in range(B):
            sample_nodes = node_features[b]

            # 调用GAT获取注意力权重
            enhanced_nodes, layer_attentions = self.gat(sample_nodes, return_attention=True)
            attention_weights_list.append(layer_attentions)

        return attention_weights_list
    
    def get_modulation_stats(self, features):
        """
        获取调制参数的统计信息用于调试
        
        Args:
            features (torch.Tensor): 输入特征 (B, C, H, W)
            
        Returns:
            dict: 调制参数统计信息
        """
        B, C, H, W = features.shape
        
        channel_features = F.adaptive_avg_pool2d(features, (1, 1))
        channel_features = channel_features.view(B, C, 1)
        node_features = self.channel_projection(channel_features)
        
        all_gammas = []
        all_betas = []
        
        for b in range(B):
            sample_nodes = node_features[b]
            enhanced_nodes = self.gat(sample_nodes)
            modulation_params = self.modulation_head(enhanced_nodes)
            
            gamma = modulation_params[:, 0]
            beta = modulation_params[:, 1]
            
            all_gammas.append(gamma)
            all_betas.append(beta)
        
        all_gammas = torch.stack(all_gammas)  # (B, C)
        all_betas = torch.stack(all_betas)    # (B, C)
        
        stats = {
            'gamma_mean': all_gammas.mean().item(),
            'gamma_std': all_gammas.std().item(),
            'gamma_min': all_gammas.min().item(),
            'gamma_max': all_gammas.max().item(),
            'beta_mean': all_betas.mean().item(),
            'beta_std': all_betas.std().item(),
            'beta_min': all_betas.min().item(),
            'beta_max': all_betas.max().item(),
        }
        
        return stats




